// Google AI (Gemini) integration for WIDDX DEV using REST API
// Based on the official Google AI REST API

/**
 * Get the appropriate Gemini model name for API
 */
function getGeminiModelName(modelName) {
  const modelMap = {
    'gemini-1.5-pro': 'gemini-1.5-pro',
    'gemini-1.5-flash': 'gemini-1.5-flash',
    'gemini-2.0-flash-exp': 'gemini-2.0-flash-exp'
  };

  return modelMap[modelName] || 'gemini-1.5-flash';
}

/**
 * Convert messages to Gemini format
 */
function convertMessagesToGeminiFormat(messages) {
  const systemMessage = messages.find(m => m.role === 'system');
  const userMessages = messages.filter(m => m.role === 'user');

  // Combine system prompt with user message
  let combinedText = '';
  if (systemMessage) {
    combinedText += `${systemMessage.content}\n\n`;
  }

  // Add user messages
  userMessages.forEach(msg => {
    combinedText += `${msg.content}\n`;
  });

  return {
    contents: [
      {
        parts: [
          {
            text: combinedText
          }
        ]
      }
    ]
  };
}

/**
 * Stream response from Gemini using REST API
 */
export async function* streamGeminiResponse(apiKey, modelName, messages, maxTokens = 8192) {
  try {
    const actualModelName = getGeminiModelName(modelName);
    const requestBody = convertMessagesToGeminiFormat(messages);

    // Add generation config
    requestBody.generationConfig = {
      temperature: 0.7,
      topP: 0.8,
      topK: 40,
      maxOutputTokens: maxTokens,
    };

    const url = `https://generativelanguage.googleapis.com/v1beta/models/${actualModelName}:streamGenerateContent?key=${apiKey}`;

    console.log('[Gemini] Starting stream request to:', url);
    console.log('[Gemini] Request body:', JSON.stringify(requestBody, null, 2));

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody)
    });

    console.log('[Gemini] Response status:', response.status);

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      console.error('[Gemini] API Error:', errorData);
      throw new Error(`Gemini API error: ${response.status} - ${errorData.error?.message || response.statusText}`);
    }

    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let buffer = '';

    while (true) {
      const { done, value } = await reader.read();
      if (done) break;

      buffer += decoder.decode(value, { stream: true });
      const lines = buffer.split('\n');

      // Keep the last incomplete line in buffer
      buffer = lines.pop() || '';

      for (const line of lines) {
        const trimmedLine = line.trim();
        if (!trimmedLine) continue;

        try {
          // Gemini streaming returns JSON objects separated by newlines
          const jsonData = JSON.parse(trimmedLine);
          console.log('[Gemini] Received chunk:', jsonData);

          const text = jsonData.candidates?.[0]?.content?.parts?.[0]?.text;

          if (text) {
            console.log('[Gemini] Yielding text:', text);
            yield {
              choices: [{
                delta: {
                  content: text
                }
              }]
            };
          }
        } catch (parseError) {
          console.warn('[Gemini] Failed to parse chunk:', trimmedLine, parseError);
        }
      }
    }

    // Process any remaining data in buffer
    if (buffer.trim()) {
      try {
        const jsonData = JSON.parse(buffer.trim());
        const text = jsonData.candidates?.[0]?.content?.parts?.[0]?.text;
        if (text) {
          yield {
            choices: [{
              delta: {
                content: text
              }
            }]
          };
        }
      } catch (parseError) {
        console.warn('[Gemini] Failed to parse final chunk:', buffer, parseError);
      }
    }

  } catch (error) {
    console.error('[Gemini] Streaming error:', error);
    throw new Error(`Gemini API error: ${error.message}`);
  }
}

/**
 * Get single response from Gemini using REST API (for modifications)
 */
export async function getGeminiResponse(apiKey, modelName, messages, maxTokens = 8192) {
  try {
    const actualModelName = getGeminiModelName(modelName);
    const requestBody = convertMessagesToGeminiFormat(messages);

    // Add generation config with lower temperature for modifications
    requestBody.generationConfig = {
      temperature: 0.3, // Lower temperature for more precise modifications
      topP: 0.8,
      topK: 40,
      maxOutputTokens: maxTokens,
    };

    const url = `https://generativelanguage.googleapis.com/v1beta/models/${actualModelName}:generateContent?key=${apiKey}`;

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(`Gemini API error: ${response.status} - ${errorData.error?.message || response.statusText}`);
    }

    const data = await response.json();
    const text = data.candidates?.[0]?.content?.parts?.[0]?.text;

    if (!text) {
      throw new Error('No content returned from Gemini API');
    }

    return {
      choices: [{
        message: {
          content: text
        }
      }]
    };

  } catch (error) {
    console.error('Gemini response error:', error);
    throw new Error(`Gemini API error: ${error.message}`);
  }
}

/**
 * Check if model is a Gemini model
 */
export function isGeminiModel(modelName) {
  return modelName && (
    modelName.includes('gemini-1.5-pro') ||
    modelName.includes('gemini-1.5-flash') ||
    modelName.includes('gemini-2.0-flash-exp')
  );
}

/**
 * Get Gemini model capabilities
 */
export function getGeminiCapabilities(modelName) {
  const capabilities = {
    'gemini-1.5-pro': {
      maxTokens: 1000000,
      supportsStreaming: true,
      supportsImages: true,
      supportsCode: true,
      bestFor: ['reasoning', 'complex-tasks', 'analysis']
    },
    'gemini-1.5-flash': {
      maxTokens: 1000000,
      supportsStreaming: true,
      supportsImages: true,
      supportsCode: true,
      bestFor: ['general', 'fast-responses', 'web-development']
    },
    'gemini-2.0-flash-exp': {
      maxTokens: 1000000,
      supportsStreaming: true,
      supportsImages: true,
      supportsCode: true,
      bestFor: ['experimental', 'latest-features', 'advanced-reasoning']
    }
  };

  return capabilities[modelName] || capabilities['gemini-1.5-flash'];
}
